import { Router } from "express";
import { ProductConversionController } from "../Controllers/productConversion";

const router = Router();
const controller = new ProductConversionController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/", controller.create.bind(controller));
router.put("/:id", controller.update.bind(controller));
router.delete("/:id", controller.delete.bind(controller));
router.post("/:id/break", controller.breakProduct.bind(controller));
router.post("/:id/combine", controller.combineProduct.bind(controller));

export default router;

