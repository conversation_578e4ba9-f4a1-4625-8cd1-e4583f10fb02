import { Router } from "express";
import { PaymentGoodsReceiptController } from "../Controllers/PaymentGoodsReceipt";

const router = Router();
const controller = new PaymentGoodsReceiptController();

router.get("/", controller.getAll.bind(controller));
router.get("/:id", controller.getById.bind(controller));
router.post("/:grId", controller.create.bind(controller));
router.delete("/:id", controller.delete.bind(controller));

export default router;
