import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { StockTransferOrder } from "./StockTransferOrder";
import { Product } from "./Product";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";

@Entity()
export class StockTransferOrderDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => StockTransferOrder)
  @JoinColumn()
  sto!: StockTransferOrder;

  @OneToMany(() => StockTransferSlipDetails, stsDetail => stsDetail.sto_details)
  sts_details!: StockTransferSlipDetails[];

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @Column({ type: "numeric" })
  quantity!: number;

  // @Column({ type: "numeric" })
  // unit_price!: number;

  // @Column({ type: "numeric" })
  // total_price!: number;

  @Column({ type: "text", nullable: true })
  lot_number!: string;
}