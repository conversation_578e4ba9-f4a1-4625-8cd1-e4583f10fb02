import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON>in<PERSON><PERSON><PERSON>n, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Branch } from "./Branch";
import { User } from "./User";
import { StockTransferOrderDetails } from "./StockTransferOrderDetails";
import { StockTransferOrder } from "./StockTransferOrder";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";
import { StockTransferSlip } from "./StockTransferSlip";
import { BranchReceiveDetails } from "./BranchReceiveDetails";
import { GoodsReceipt } from "./GoodsReceipt";
import { Supplier } from "./Supplier";

@Entity()
export class PaymentGoodsReceipt {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  code!: string;

  @Column({ type: 'float' })
  total_net!: number;

  @Column({ type: 'float' })
  paid_amount!: number;

  @Column({ type: 'float' })
  unpaid_amount!: number;

  @Column({ type: 'float' })
  change_amount!: number;

  @Column({ type: 'text' })
  payment_type!: string;

  @Column({ type: "date" })
  payment_date!: Date;

  @Column({ type: 'float' }) //จำนวนเงินรับมา
  cash_amount!: number;

  @Column({ type: 'float' })
  total_amount!: number;

  @Column({ type: 'text' })
  note!: string;

  @ManyToOne(() => Supplier, { onDelete: "CASCADE" }) //ผู้รับ
  @JoinColumn()
  receivedBy!: Supplier;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @ManyToOne(() => GoodsReceipt, { onDelete: "CASCADE" })
  @JoinColumn()
  gr!: GoodsReceipt;

}