import { <PERSON>umn, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Product } from "./Product";
import { PurchaseOrderItem } from "./PurchaseOrderItem";
import { GoodsReceipt } from "./GoodsReceipt";
@Entity()
export class GoodsReceiptDetails {
  @PrimaryGeneratedColumn()
  id!: number;

  @ManyToOne(() => PurchaseOrderItem, { onDelete: "CASCADE" })
  @JoinColumn()
  po_details!: PurchaseOrderItem;

  @ManyToOne(() => GoodsReceipt, { onDelete: "CASCADE" })
  @JoinColumn()
  gr!: GoodsReceipt;

  @ManyToOne(() => Product, { onDelete: "CASCADE" })
  @JoinColumn()
  product!: Product;

  @Column({ type: 'text' })
  lot_number_before!: string;

  @Column({ type: 'numeric' })
  receive_quantity!: number;

  @Column({ type: 'text' })
  receive_unit!: string;

  @Column({}) //มีส่วนลดหรือไม่
  has_discount!: boolean;

  @Column({ type: 'float' })
  receive_price_before_tax!: number; //ราคารับไม่รวมภาษี

  @Column({ type: 'float' })
  receive_price_after_discount!: number; //ราคารับหลังลด

  @Column({}) //มีสินค้าแถมหรือไม่
  has_free!: boolean;

  @Column({ type: 'numeric' })
  free_quantity!: number;

  @Column({ type: 'text' })
  free_unit!: string;

  @Column({ type: 'text' }) //เลขที่ผลิต
  production_number!: string;

  @Column({ type: 'date' })
  mfg_date!: Date; //วันที่ผลิต

  @Column({ type: 'date' })
  exp_date!: Date; //วันที่หมดอายุ

  @Column({ type: 'numeric' })
  total_receive_quantity!: number; //จำนวนรับทั้งหมด

  @Column({ type: 'float' })
  cost_unit!: number; //ราคาทุนต่อหน่วย

  @Column({ type: 'float' })
  total_price_product!: number; //ราคารวมของแต่ละสินค้า

  @Column({ type: 'float' })
  gr_total!: number; //ราคารวม

}