import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { User } from "./User";
import { PurchaseOrder } from "./PurchaseOrder";
import { Supplier } from "./Supplier";
import { GoodsReceiptDetails } from "./GoodsReceiptDetails";
import { PaymentGoodsReceipt } from "./PaymentGoodsReceipt";


@Entity()
export class GoodsReceipt {
  @PrimaryGeneratedColumn()
  id!: number;

  @Column({ type: 'text' })
  code!: string;

  @OneToOne(() => PurchaseOrder, { onDelete: "CASCADE" })
  @JoinColumn()
  po!: PurchaseOrder;

  @ManyToOne(() => Supplier, { onDelete: "CASCADE" }) //บริษัทจำหน่าย
  @JoinColumn()
  distributor!: Supplier;

  @Column({ type: "datetime", nullable: true })
  po_date!: Date;

  @Column({ type: "text", nullable: true })
  po_code!: string;

  @Column({ type: "datetime" })
  date_document!: Date; //วันที่เอกสาร

  @Column({ type: "datetime", nullable: true })
  receive_date!: Date; //วันที่รับสินค้า

  @Column({ type: "float", nullable: true })
  po_total!: number;

  @Column({ type: "float" }) //ภาษี
  tax!: number;

  @Column({ type: "float" }) //รวมภาษี
  tax_total!: number;

  @Column({ type: "float" }) //รวมเงินชำระ
  gr_total!: number;

  @Column({ type: "text", nullable: true })
  tax_invoice_number!: string; //ใบกำกับภาษี

  @Column({ type: "text" })
  status!: string;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @Column({ type: "text", nullable: true })
  product_price_tax!: string;

  @Column({ type: "text", nullable: true })
  order_discount_tax!: string;

  @Column({ type: 'text', nullable: true })
  note!: string;

  @OneToMany(() => GoodsReceiptDetails, (gr_details) => gr_details.gr)
  gr_details!: GoodsReceiptDetails[];

  @OneToMany(() => PaymentGoodsReceipt, (payment) => payment.gr)
  payment!: GoodsReceipt[];
}