// id: int(PK)
// sto_id: int(FK)
// code: string
// status: string
// source_branch: Branch
// destination_branch: Branch
// transfer_date: datetime
// user: User
// note: string
// sts_price: int

import { Column, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne, PrimaryGeneratedColumn } from "typeorm";
import { Branch } from "./Branch";
import { User } from "./User";
import { StockTransferOrderDetails } from "./StockTransferOrderDetails";
import { StockTransferOrder } from "./StockTransferOrder";
import { StockTransferSlipDetails } from "./StockTransferSlipDetails";
import { StockTransferSlip } from "./StockTransferSlip";
import { BranchReceiveDetails } from "./BranchReceiveDetails";

// sts_price: int
@Entity()
export class BranchReceive {
  @PrimaryGeneratedColumn()
  id!: number;

  @OneToOne(() => StockTransferSlip, { onDelete: "CASCADE" })
  @JoinColumn()
  sts!: StockTransferSlip;

  @Column({ type: 'text' })
  code!: string;

  @Column({ type: 'text' })
  status!: string;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  source_branch!: Branch;

  @ManyToOne(() => Branch, { onDelete: "CASCADE" })
  @JoinColumn()
  destination_branch!: Branch;

  @Column({ type: 'text' })
  sts_code!: string;

  @Column({ type: "datetime" })
  receive_date!: Date;

  @Column({ type: "datetime", nullable: true })
  complete_date!: Date;

  @Column({ type: 'text' })
  note!: string;

  @ManyToOne(() => User, { onDelete: "CASCADE" })
  @JoinColumn()
  user!: User;

  @OneToMany(() => BranchReceiveDetails, (br_details) => br_details.br)
  br_details!: BranchReceiveDetails[];
}