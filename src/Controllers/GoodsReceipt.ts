import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { Supplier } from "../Models/Supplier";
import { User } from "../Models/User";
import { Branch } from "../Models/Branch";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { Stock } from "../Models/Stock";
import { GoodsReceipt } from "../Models/GoodsReceipt";
import { GoodsReceiptDetails } from "../Models/GoodsReceiptDetails";

export class GoodsReceiptController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const grRepository = AppDataSource.getRepository(GoodsReceipt);
      const gr = await grRepository.find({
        relations: ["po", "distributor", "gr_details", "payment"]
      });
      res.status(200).json(gr);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const grRepository = AppDataSource.getRepository(StockTransferOrder);
      const gr = await grRepository.findOne({
        where: { id: Number(id) },
        relations: ["po", "distributor", "gr_details", "payment"]
      });
      if (gr) {
        res.status(200).json(gr);
      } else {
        res.status(404).json({ message: "GR not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getProductByDistrbutor(req: Request, res: Response): Promise<void> { //ไว้ใช้แสดง product เลือกตอน add product เข้าใบ GR
    const { id } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const grRepository = AppDataSource.getRepository(GoodsReceipt)
      const gr = await grRepository.findOne({
        where: { id: Number(id) },
        relations: ["po", "distributor", "gr_details", "payment"]
      });
      if (!gr) {
        res.status(404).json({ message: "GR not found" });
        return;
      }
      const product = await stockRepository.find({
        where: { branch: { id: gr?.distributor.id } },
        relations: ["product"]
      })
      if (product) {
        res.status(200).json({
          branch: gr.distributor,
          products: product,
        });
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async createFromDistributor(req: Request, res: Response): Promise<void> { //createfromdistributor
    const userRepository = AppDataSource.getRepository(User);
    const branchRepository = AppDataSource.getRepository(Branch);
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const distributorRepository = AppDataSource.getRepository(Supplier);


    const {
      distributor,
      date_document,
      user,
      tax = 0.00,
      tax_total = 0.00,
      gr_total = 0.00,
      note = '',
      gr_details = [] // ถ้าไม่ได้ส่งมาก็ให้เป็น array ว่าง
    } = req.body;

    try {
      const userEntity = await userRepository.findOneByOrFail({ id: user.id });
      const distributorEntity = await distributorRepository.findOneByOrFail({ id: distributor.id });

      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date(date_document).toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastGR = await grRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastGR.length > 0 ? lastGR[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `GR-${formattedDate}-${lastId}`;

      // สร้าง STO ใหม่
      const newGR = grRepository.create({
        code: generatedCode,
        user: userEntity,
        distributor: distributorEntity,
        date_document: date_document,
        tax,
        tax_total,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
        gr_total,
        gr_details, // จะเป็น [] ถ้าไม่ได้ส่งมา
        note: note
      });

      const grDetailsWithRelation = gr_details.map((detail: any) => {
        return {
          ...detail,
          gr: newGR, // เชื่อมความสัมพันธ์กลับ
        };
      });

      newGR.gr_details = grDetailsWithRelation;
      const savedGR = await grRepository.save(newGR);

      res.status(201).json(savedGR);
    } catch (error) {
      console.error("Error creating GR:", error);
      res.status(500).json({ message: "Failed to create GR", error });
    }
  }

  public async createGRByPO(req: Request, res: Response): Promise<void> {
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    const poRepository = AppDataSource.getRepository(PurchaseOrder);
    const stockRepository = AppDataSource.getRepository(Stock);
    const { poId } = req.params
    console.log(poId)
    const po = await poRepository.findOneOrFail({
      where: { id: Number(poId) },
      relations: ["supplier", "purchase_order_items", "purchase_order_items.product"]
    });
    try {
      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date().toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastGR = await grRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastGR.length > 0 ? lastGR[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `GR-${formattedDate}-${lastId}-${po.code}`;

      // สร้าง STS ใหม่
      const newGR = grRepository.create({
        po: po,
        code: generatedCode,
        po_code: po.code,
        distributor: po.supplier,
        date_document: new Date(),
        user: po.user,
        tax: 0.00,
        tax_total: 0.00,
        gr_total: 0.00,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
      });
      // บันทึกลง DB เพื่อให้ได้ newSts.id
      const savedGR = await grRepository.save(newGR);

      // แปลง detail และผูกกลับหา parent
      const grDetailRepository = AppDataSource.getRepository(GoodsReceiptDetails);

      let tax_total = 0.00

      const grDetails = await Promise.all(
        po.purchase_order_items.map(async (detail) => {
          console.log('detail.product:', detail.product);
          const lotStock = await stockRepository.findOneOrFail({
            where: {
              // branch: { id: po.supplier.id },
              product: { id: detail.product.id }
            },
            relations: ["product", "branch"]
          });
          tax_total += (detail.quantity * detail.unit_price)
          let tax = ((detail.quantity * detail.unit_price) * 7) / 100
          return grDetailRepository.create({
            gr: newGR,
            product: detail.product,
            receive_quantity: detail.quantity,
            receive_unit: detail.product.unit,
            receive_price_before_tax: detail.unit_price,
            receive_price_after_discount: detail.unit_price,
            free_quantity: 0,
            free_unit: detail.product.unit,
            total_receive_quantity: detail.quantity,  //+free_quantity
            cost_unit: detail.product.standard_cost,
            total_price_product: tax + (detail.quantity * detail.unit_price),
            lot_number_before: lotStock.lot_number ?? '',
            po_details: detail,
          });
        })
      );

      let tax = (tax_total * 7) / 100
      tax_total += tax
      // save ทั้ง array
      await grDetailRepository.save(grDetails);
      console.log(grDetails)
      savedGR.gr_details = grDetails;
      await grRepository.update(savedGR.id, {
        tax: tax,
        tax_total: tax_total
      });

      //upate gr in po
      // await grRepository.update(sto.id, {
      //   sts: savedSTS
      // });

      const response = {
        ...savedGR,
        gr_details: grDetails.map(detail => ({
          id: detail.id,
          product: detail.product,
          receive_quantity: detail.receive_quantity,
          receive_unit: detail.receive_unit,
          receive_price_before_tax: detail.receive_price_before_tax,
          receive_price_after_discount: detail.receive_price_after_discount,
          free_quantity: detail.free_quantity,
          free_unit: detail.free_unit,
          total_receive_quantity: detail.total_receive_quantity,  //+free_quantity
          cost_unit: detail.cost_unit,
          total_price_product: detail.total_price_product,
          lot_number_before: detail.lot_number_before,
        }))
      };

      res.status(201).json(response);

      // res.status(201).json(savedSTS);
    } catch (error) {
      console.error("Error creating GR:", error);
      res.status(500).json({ message: "Failed to create GR", error });
    }
  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    try {
      const gr = await grRepository.findOne({ where: { id: Number(id) } });
      if (gr) {
        grRepository.merge(gr, req.body);
        const updatedGR = await grRepository.save(gr);
        res.status(200).json(updatedGR);
      } else {
        res.status(404).json({ message: "Goods Receipt not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Goods Receipt", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const grRepository = AppDataSource.getRepository(GoodsReceipt);
    try {
      const result = await grRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Goods Receipt not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}