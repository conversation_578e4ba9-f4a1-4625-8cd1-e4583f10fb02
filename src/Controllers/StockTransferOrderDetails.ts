import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { Product } from "../Models/Product";
import { In } from "typeorm";
import { StockTransferOrderDetails } from "../Models/StockTransferOrderDetails";
import { StockTransferOrder } from "../Models/StockTransferOrder";

export class StockTransferOrderDetailsController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const stoDetailsRepository = AppDataSource.getRepository(StockTransferOrderDetails);
      const stoDetails = await stoDetailsRepository.find({ relations: ["sto", "product"] });
      res.status(200).json(stoDetails);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stoDetailsRepository = AppDataSource.getRepository(StockTransferOrderDetails);
      const stoDetails = await stoDetailsRepository.findOne({
        where: { id: Number(id) },
        relations: ["sto", "product"]
      });
      if (stoDetails) {
        res.status(200).json(stoDetails);
      } else {
        res.status(404).json({ message: "STO details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const stoDetailsRepository = AppDataSource.getRepository(StockTransferOrderDetails);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);
    const productRepository = AppDataSource.getRepository(Product);

    try {
      const stoId = parseInt(req.params.stoId, 10);
      if (isNaN(stoId)) {
        res.status(400).json({ message: "Invalid stoId" });
        return;
      }

      const stoDetails = req.body;
      if (!Array.isArray(stoDetails)) {
        res.status(400).json({ message: "Invalid request body" });
        return;
      }

      const sto = await stoRepository.findOne({
        where: { id: stoId }
      });

      if (!sto) {
        res.status(404).json({ message: "STO Details not found" });
        return;
      }

      // ✅ ดึงข้อมูล Order Items ที่มีอยู่เดิม
      const existingItems = await stoDetailsRepository.find({
        where: { sto: { id: stoId } }
      });
      console.log(existingItems)

      // ✅ ดึงข้อมูลสินค้า
      const productIds = stoDetails
        .filter(item => typeof item.product === 'object' || typeof item.product_id === 'number')
        .map(item => (typeof item.product === 'object' ? item.product.id : item.product_id));

      const products = await productRepository.find({ where: productIds.map(id => ({ id })) });
      console.log(stoDetails)
      // ✅ เตรียมรายการใหม่ (เพิ่ม + อัปเดต)
      const updatedItems = stoDetails.map(item => {
        let product: Product | undefined;

        if (typeof item.product === 'object') {
          product = products.find(p => p.id === item.product.id);
        } else if (typeof item.product_id === 'number') {
          product = products.find(p => p.id === item.product_id);
        }

        if (!product) return null;

        if (item.id === 0) {
          return stoDetailsRepository.create({
            sto: sto,
            product,
            quantity: item.quantity,
            // unit_price: item.unit_price,
            // total_price: item.quantity * item.unit_price,
          });
        } else {
          const existingItem = existingItems.find(i => i.id === item.id);
          if (existingItem) {
            existingItem.quantity = item.quantity;
            // existingItem.unit_price = item.unit_price;
            // existingItem.total_price = existingItem.quantity * item.unit_price;
            return existingItem;
          }
        }
        return null;
      }).filter((item): item is StockTransferOrderDetails => item !== null);

      // ✅ ลบ Order Items ที่ไม่มีอยู่ในรายการใหม่
      const receivedItemIds = stoDetails.map(item => item.id);
      const itemsToDelete = existingItems.filter(item => !receivedItemIds.includes(item.id));

      if (itemsToDelete.length > 0) {
        await stoDetailsRepository.remove(itemsToDelete);
      }

      // ✅ ป้องกัน `save([])`
      if (updatedItems.length > 0) {
        const savedItems = await stoDetailsRepository.save(updatedItems);

        // 🔥 ✅ คำนวณค่า po_total & order_total ใหม่
        // const updatedOrderItems = await purchaseOrderItemRepository.find({
        //   where: { purchase_order: { id: purchaseOrderId } }
        // });

        // const poTotal = updatedOrderItems.reduce((sum, item) => sum + item.total_price, 0);
        // const orderTotal = updatedOrderItems.reduce((sum, item) => sum + item.quantity, 0);

        // // 🔥 ✅ อัปเดตค่ารวมใน Purchase Order
        // purchaseOrder.po_total = poTotal;
        // purchaseOrder.order_total = orderTotal;
        await stoRepository.save(sto);

        res.status(201).json({
          message: "STO Details updated successfully",
          stoDetails: savedItems,
        });
      } else {
        res.status(200).json({
          message: "No updates were made",
          stoDetails: [],
        });
      }

    } catch (error) {
      console.error("Error updating STO Details:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }






  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
    try {
      const purchaseOrderItem = await purchaseOrderItemRepository.findOne({ where: { id: Number(id) } });
      if (purchaseOrderItem) {
        purchaseOrderItemRepository.merge(purchaseOrderItem, req.body);
        purchaseOrderItem.total_price = purchaseOrderItem.quantity * purchaseOrderItem.unit_price;
        const updatedPurchaseOrderItem = await purchaseOrderItemRepository.save(purchaseOrderItem);
        res.status(200).json(updatedPurchaseOrderItem);
      } else {
        res.status(404).json({ message: "Purchase Order Item not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Purchase Order Item" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);
    try {
      const result = await purchaseOrderItemRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Purchase Order Item not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async updateMultiple(req: Request, res: Response): Promise<void> {
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);

    try {
      const purchase_order_id = Number(req.params.id);
      console.log("Received purchase_order_id:", purchase_order_id);
      console.log("Received body:", req.body);

      // ถ้า req.body เป็น array ให้ใช้ได้เลย, ถ้าเป็น object ให้ดึง items
      const items = Array.isArray(req.body) ? req.body : req.body.items;

      if (!items || !Array.isArray(items) || items.length === 0) {
        res.status(400).json({ message: "Invalid request data" });
        return;
      }

      // หา PurchaseOrderItem ทั้งหมดที่ต้องอัปเดต
      const itemIds = items.map((item) => item.id);
      const existingItems = await purchaseOrderItemRepository.findBy({ id: In(itemIds) });
      if (existingItems.length !== items.length) {
        res.status(404).json({ message: "Some Purchase Order Items not found" });
        return;
      }

      // อัปเดตค่าใหม่
      const updatedItems = existingItems.map((existingItem) => {
        const newItem = items.find((item) => item.id === existingItem.id);
        if (newItem) {
          return {
            ...existingItem,
            quantity: newItem.quantity,
            unit_price: newItem.unit_price,
            total_price: newItem.quantity * newItem.unit_price,
          };
        }
        return existingItem;
      });

      // บันทึกการอัปเดตทั้งหมด
      await purchaseOrderItemRepository.save(updatedItems);

      res.status(200).json({ message: "Purchase Order Items updated successfully", updatedItems });
    } catch (error) {
      res.status(500).json({ message: "Error updating Purchase Order Items", error });
    }
  }



  public async deleteMultiple(req: Request, res: Response): Promise<void> {
    console.log("🔹 DELETE API CALLED");
    console.log("🔹 Received request params:", req.params);

    const { ids } = req.params;  // รับค่า ids จาก URL parameter
    console.log("🔹 Extracted IDs from params:", ids);

    // แปลงค่าจาก string (เช่น "1,2,3") ให้เป็น array ของตัวเลข
    const idsArray = ids.split(",").map((id) => parseInt(id, 10));

    const stoDetailsRepository = AppDataSource.getRepository(StockTransferOrderDetails);

    try {
      if (!idsArray || idsArray.length === 0) {
        res.status(400).json({ message: "Invalid request data" });
        return;
      }

      const result = await stoDetailsRepository.delete(idsArray);
      console.log("Delete result:", result);

      const itemsToDelete = await stoDetailsRepository.findBy({ id: In(idsArray) });
      console.log("Items found in DB:", itemsToDelete);

      if (itemsToDelete.length === 0) {
        res.status(404).json({ message: "Some Purchase Order Items not found" });
        return;
      }

      await stoDetailsRepository.remove(itemsToDelete);
      res.status(204).send();

    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

}
