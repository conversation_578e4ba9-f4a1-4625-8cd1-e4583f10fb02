import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { Product } from "../Models/Product";
import { In } from "typeorm";
import { StockTransferSlipDetails } from "../Models/StockTransferSlipDetails";
import { StockTransferSlip } from "../Models/StockTransferSlip";
import { Stock } from "../Models/Stock";

export class StockTransferSlipDetailsController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
      const stsDetails = await stsDetailsRepository.find({ relations: ["sts", "product"] });
      res.status(200).json(stsDetails);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
      const stsDetails = await stsDetailsRepository.findOne({
        where: { id: Number(id) },
        relations: ["sts", "product"]
      });
      if (stsDetails) {
        res.status(200).json(stsDetails);
      } else {
        res.status(404).json({ message: "STO details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> { //manual
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const productRepository = AppDataSource.getRepository(Product);
    const stockRepository = AppDataSource.getRepository(Stock);
    try {
      const stsId = parseInt(req.params.stsId, 10);
      if (isNaN(stsId)) {
        res.status(400).json({ message: "Invalid stoId" });
        return;
      }

      const stsDetails = req.body;
      if (!Array.isArray(stsDetails)) {
        res.status(400).json({ message: "Invalid request body" });
        return;
      }

      const sts = await stsRepository.findOne({
        where: { id: stsId },
        relations: ["source_branch", "destination_branch"]
      });

      if (!sts) {
        res.status(404).json({ message: "STS Details not found" });
        return;
      }

      if (sts.status !== 'เตรียมรายการ') {
        res.status(400).json({ message: "STS status must be 'เตรียมรายการ' to update" });
        return;
      }

      // ✅ ดึงข้อมูล Order Items ที่มีอยู่เดิม
      const existingItems = await stsDetailsRepository.find({
        where: { sts: { id: stsId } }
      });
      console.log(existingItems)

      // ✅ ดึงข้อมูลสินค้า
      const productIds = stsDetails
        .filter(item => typeof item.product === 'object' || typeof item.product_id === 'number')
        .map(item => (typeof item.product === 'object' ? item.product.id : item.product_id));

      const products = await productRepository.find({ where: productIds.map(id => ({ id })) });
      console.log(stsDetails)
      console.log(productIds)

      // ดึง stock ตามสินค้าที่เกี่ยวข้อง + branch ต้นทาง version สร้าง sts เอง
      const allStock = await stockRepository.find({
        where: {
          product: In(productIds),
          branch: { id: sts.source_branch.id }
        },
        relations: ["product", "branch"]
      });

      console.log(allStock)

      // ✅ เตรียมรายการใหม่ (เพิ่ม + อัปเดต)
      const updatedItems = stsDetails.map(item => {
        let product: Product | undefined;

        if (typeof item.product === 'object') {
          product = products.find(p => p.id === item.product.id);
        } else if (typeof item.product_id === 'number') {
          product = products.find(p => p.id === item.product_id);
        }

        if (!product) return null;

        const stockMatch = allStock.find(
          stock => stock.product.id === product!.id && stock.branch.id === sts.source_branch.id
        );

        const lot_number = stockMatch?.lot_number ?? '';

        if (item.id === 0) {
          console.log(item)
          return stsDetailsRepository.create({
            sts: sts,
            product,
            sts_quantity: 0,
            lot_number,
            sto_quantity: item.sto_quantity,
            sts_details_status: 'จัดเตรียมสินค้า'
          });
        } else {
          const existingItem = existingItems.find(i => i.id === item.id);
          if (existingItem) {
            existingItem.sto_quantity = item.sto_quantity;
            existingItem.sts_quantity = item.sts_quantity;
            existingItem.sts_details_status = item.sts_details_status
            return existingItem;
          }
        }
        return null;
      }).filter((item): item is StockTransferSlipDetails => item !== null);

      // ✅ ลบ Order Items ที่ไม่มีอยู่ในรายการใหม่
      const receivedItemIds = stsDetails.map(item => item.id);
      const itemsToDelete = existingItems.filter(item => !receivedItemIds.includes(item.id));

      if (itemsToDelete.length > 0) {
        await stsDetailsRepository.remove(itemsToDelete);
      }

      // ✅ ป้องกัน `save([])`
      if (updatedItems.length > 0) {
        const savedItems = await stsDetailsRepository.save(updatedItems);
        await stsRepository.save(sts);

        res.status(201).json({
          savedItems,
        });
      } else {
        res.status(200).json({
          stsDetails: [],
        });
      }

    } catch (error) {
      console.error("Error updating STS Details:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async updateSTSDetailsFromSTO(req: Request, res: Response): Promise<void> {
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const productRepository = AppDataSource.getRepository(Product);

    const stsId = parseInt(req.params.stsId, 10);
    if (isNaN(stsId)) {
      res.status(400).json({ message: "Invalid stsId" });
      return;
    }

    const incomingItems = req.body;
    if (!Array.isArray(incomingItems)) {
      res.status(400).json({ message: "Invalid request body" });
      return;
    }

    const sts = await stsRepository.findOne({ where: { id: stsId } });
    if (!sts) {
      res.status(404).json({ message: "STS not found" });
      return;
    }

    // ดึงรายการเดิมทั้งหมดในฐานข้อมูล
    const existingItems = await stsDetailsRepository.find({
      where: { sts: { id: stsId } },
      relations: ['product']
    });

    // ดึง products ที่เกี่ยวข้องมาเพื่อ map
    const productIds = incomingItems.map(i => i.product?.id || i.product_id);
    const products = await productRepository.findByIds(productIds);

    const itemsToUpdate = incomingItems.map(item => {
      const existingItem = existingItems.find(i => i.id === item.id);
      if (!existingItem) return null;

      const product = products.find(p =>
        typeof item.product === 'object'
          ? p.id === item.product.id
          : p.id === item.product_id
      );
      if (!product) return null;
      // if (item.sts_quantity == 0){
      //   existingItem.sts_details_status
      // }
      existingItem.product = product;
      existingItem.sto_quantity = item.sto_quantity;
      existingItem.sts_quantity = item.sts_quantity;
      existingItem.lot_number = item.lot_number;
      existingItem.sts_details_status = item.sts_details_status;

      return existingItem;
    }).filter((item): item is StockTransferSlipDetails => item !== null);

    await stsDetailsRepository.save(itemsToUpdate);

    res.status(200).json(itemsToUpdate);
  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    try {
      const stsDetails = await stsDetailsRepository.findOne({ where: { id: Number(id) } });
      if (stsDetails) {
        stsDetailsRepository.merge(stsDetails, req.body);
        const updatedstsDetails = await stsDetailsRepository.save(stsDetails);
        res.status(200).json(updatedstsDetails);
      } else {
        res.status(404).json({ message: "STS Details not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating STS Details" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const stsDetailsRepository = AppDataSource.getRepository(StockTransferSlipDetails);
    try {
      const result = await stsDetailsRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "STS Details not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async updateMultiple(req: Request, res: Response): Promise<void> {
    const purchaseOrderItemRepository = AppDataSource.getRepository(PurchaseOrderItem);

    try {
      const purchase_order_id = Number(req.params.id);
      console.log("Received purchase_order_id:", purchase_order_id);
      console.log("Received body:", req.body);

      // ถ้า req.body เป็น array ให้ใช้ได้เลย, ถ้าเป็น object ให้ดึง items
      const items = Array.isArray(req.body) ? req.body : req.body.items;

      if (!items || !Array.isArray(items) || items.length === 0) {
        res.status(400).json({ message: "Invalid request data" });
        return;
      }

      // หา PurchaseOrderItem ทั้งหมดที่ต้องอัปเดต
      const itemIds = items.map((item) => item.id);
      const existingItems = await purchaseOrderItemRepository.findBy({ id: In(itemIds) });
      if (existingItems.length !== items.length) {
        res.status(404).json({ message: "Some Purchase Order Items not found" });
        return;
      }

      // อัปเดตค่าใหม่
      const updatedItems = existingItems.map((existingItem) => {
        const newItem = items.find((item) => item.id === existingItem.id);
        if (newItem) {
          return {
            ...existingItem,
            quantity: newItem.quantity,
            unit_price: newItem.unit_price,
            total_price: newItem.quantity * newItem.unit_price,
          };
        }
        return existingItem;
      });

      // บันทึกการอัปเดตทั้งหมด
      await purchaseOrderItemRepository.save(updatedItems);

      res.status(200).json({ message: "Purchase Order Items updated successfully", updatedItems });
    } catch (error) {
      res.status(500).json({ message: "Error updating Purchase Order Items", error });
    }
  }


}
