import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { WorkingSchedule } from "../Models/WorkingSchedule";
import { User } from "../Models/User";

export class WorkingScheduleController {
  // Get working schedule for a specific user
  public async getUserSchedule(req: Request, res: Response): Promise<void> {
    const workingScheduleRepository = AppDataSource.getRepository(WorkingSchedule);
    
    try {
      const userId = Number(req.params.userId);
      
      if (!userId) {
        res.status(400).json({ message: "userId is required" });
        return;
      }
      
      const schedule = await workingScheduleRepository.findOne({
        where: { 
          user: { id: userId },
          is_active: true 
        },
        relations: ["user"],
      });
      
      if (!schedule) {
        res.status(404).json({ message: "Working schedule not found for this user" });
        return;
      }
      
      // Clean up response data
      const { user, ...scheduleData } = schedule;
      res.status(200).json({
        ...scheduleData,
        user: {
          id: user.id,
          name: user.name,
        },
      });
    } catch (error) {
      console.error("Get user schedule error:", error);
      res.status(500).json({ 
        message: "Server error while fetching user schedule" 
      });
    }
  }

  // Create or update working schedule for a user
  public async createOrUpdateSchedule(req: Request, res: Response): Promise<void> {
    const workingScheduleRepository = AppDataSource.getRepository(WorkingSchedule);
    const userRepository = AppDataSource.getRepository(User);
    
    try {
      const { 
        userId, 
        standard_check_in_time, 
        standard_check_out_time, 
        late_threshold_minutes, 
        early_threshold_minutes 
      } = req.body;
      
      if (!userId || !standard_check_in_time || !standard_check_out_time) {
        res.status(400).json({ 
          message: "userId, standard_check_in_time, and standard_check_out_time are required" 
        });
        return;
      }
      
      // Check if user exists
      const user = await userRepository.findOne({
        where: { id: userId }
      });
      
      if (!user) {
        res.status(404).json({ message: "User not found" });
        return;
      }
      
      // Check if schedule already exists
      let schedule = await workingScheduleRepository.findOne({
        where: { 
          user: { id: userId },
          is_active: true 
        }
      });
      
      if (schedule) {
        // Update existing schedule
        schedule.standard_check_in_time = standard_check_in_time;
        schedule.standard_check_out_time = standard_check_out_time;
        schedule.late_threshold_minutes = late_threshold_minutes || 5;
        schedule.early_threshold_minutes = early_threshold_minutes || 30;
        schedule.updated_at = new Date();
      } else {
        // Create new schedule
        schedule = workingScheduleRepository.create({
          user,
          standard_check_in_time,
          standard_check_out_time,
          late_threshold_minutes: late_threshold_minutes || 5,
          early_threshold_minutes: early_threshold_minutes || 30,
          is_active: true,
        });
      }
      
      const savedSchedule = await workingScheduleRepository.save(schedule);
      
      // Clean up response data
      const { user: scheduleUser, ...scheduleData } = savedSchedule;
      res.status(200).json({
        ...scheduleData,
        user: {
          id: user.id,
          name: user.name,
        },
      });
    } catch (error) {
      console.error("Create/Update schedule error:", error);
      res.status(500).json({ 
        message: "Server error while saving schedule" 
      });
    }
  }

  // Get all working schedules
  public async getAllSchedules(req: Request, res: Response): Promise<void> {
    const workingScheduleRepository = AppDataSource.getRepository(WorkingSchedule);
    
    try {
      const schedules = await workingScheduleRepository.find({
        where: { is_active: true },
        relations: ["user"],
        order: { created_at: "DESC" },
      });
      
      const formattedSchedules = schedules.map(schedule => {
        const { user, ...scheduleData } = schedule;
        return {
          ...scheduleData,
          user: {
            id: user.id,
            name: user.name,
          },
        };
      });
      
      res.status(200).json(formattedSchedules);
    } catch (error) {
      console.error("Get all schedules error:", error);
      res.status(500).json({ 
        message: "Server error while fetching schedules" 
      });
    }
  }

  // Delete working schedule
  public async deleteSchedule(req: Request, res: Response): Promise<void> {
    const workingScheduleRepository = AppDataSource.getRepository(WorkingSchedule);
    
    try {
      const scheduleId = Number(req.params.scheduleId);
      
      if (!scheduleId) {
        res.status(400).json({ message: "scheduleId is required" });
        return;
      }
      
      const schedule = await workingScheduleRepository.findOne({
        where: { id: scheduleId }
      });
      
      if (!schedule) {
        res.status(404).json({ message: "Working schedule not found" });
        return;
      }
      
      // Soft delete by setting is_active to false
      schedule.is_active = false;
      schedule.updated_at = new Date();
      await workingScheduleRepository.save(schedule);
      
      res.status(200).json({ message: "Working schedule deleted successfully" });
    } catch (error) {
      console.error("Delete schedule error:", error);
      res.status(500).json({ 
        message: "Server error while deleting schedule" 
      });
    }
  }

  // Helper method to get user's active schedule (for use in attendance logic)
  public static async getUserActiveSchedule(userId: number): Promise<WorkingSchedule | null> {
    const workingScheduleRepository = AppDataSource.getRepository(WorkingSchedule);
    
    try {
      const schedule = await workingScheduleRepository.findOne({
        where: { 
          user: { id: userId },
          is_active: true 
        },
        relations: ["user"],
      });
      
      return schedule;
    } catch (error) {
      console.error("Get user active schedule error:", error);
      return null;
    }
  }
}
