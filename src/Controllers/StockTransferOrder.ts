import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { PurchaseOrderItem } from "../Models/PurchaseOrderItem";
import { Supplier } from "../Models/Supplier";
import { User } from "../Models/User";
import { Product } from "../Models/Product";
import { Branch } from "../Models/Branch";
import { StockTransferOrder } from "../Models/StockTransferOrder";
import { Stock } from "../Models/Stock";

export class StockTransferOrderController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const stoRepository = AppDataSource.getRepository(StockTransferOrder);
      const sto = await stoRepository.find({
        relations: ["sts", "user", "source_branch", "destination_branch", "sto_details"]
      });
      res.status(200).json(sto);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const stoRepository = AppDataSource.getRepository(StockTransferOrder);
      const sto = await stoRepository.findOne({
        where: { id: Number(id) },
        relations: ["sts", "user", "source_branch", "destination_branch", "sto_details", "sto_details.product"]
      });
      if (sto) {
        res.status(200).json(sto);
      } else {
        res.status(404).json({ message: "STO not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getProductByBranch(req: Request, res: Response): Promise<void> { //destination branch
    const { id } = req.params;
    try {
      const stockRepository = AppDataSource.getRepository(Stock);
      const stoRepository = AppDataSource.getRepository(StockTransferOrder)
      const sto = await stoRepository.findOne({
        where: { id: Number(id) },
        relations: ["sts", "user", "source_branch", "destination_branch", "sto_details", "sto_details.product"]
      });
      if (!sto) {
        res.status(404).json({ message: "STO not found" });
        return;
      }
      const product = await stockRepository.find({
        where: { branch: { id: sto?.destination_branch.id } },
        relations: ["product"]
      })
      if (product) {
        res.status(200).json({
          branch: sto.destination_branch,
          products: product,
        });
      } else {
        res.status(404).json({ message: "Product not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const userRepository = AppDataSource.getRepository(User);
    const branchRepository = AppDataSource.getRepository(Branch);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);

    const {
      source_branch,
      destination_branch,
      request_date,
      user,
      note,
      sto_price = 0,
      sto_details = [] // ถ้าไม่ได้ส่งมาก็ให้เป็น array ว่าง
    } = req.body;

    try {
      const userEntity = await userRepository.findOneByOrFail({ id: user.id });
      const sourceBranch = await branchRepository.findOneByOrFail({ id: source_branch.id });
      const destinationBranch = await branchRepository.findOneByOrFail({ id: destination_branch.id });

      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date(request_date).toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastSto = await stoRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastSto.length > 0 ? lastSto[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `STO-${formattedDate}-${lastId}`;

      // สร้าง STO ใหม่
      const newSto = stoRepository.create({
        code: generatedCode,
        source_branch: sourceBranch,
        destination_branch: destinationBranch,
        request_date,
        user: userEntity,
        note,
        status: 'เตรียมรายการ', // กำหนดสถานะเริ่มต้น
        sto_price,
        sto_details // จะเป็น [] ถ้าไม่ได้ส่งมา
      });

      const stoDetailsWithRelation = sto_details.map((detail: any) => {
        return {
          ...detail,
          sto: newSto, // เชื่อมความสัมพันธ์กลับ
        };
      });

      newSto.sto_details = stoDetailsWithRelation;
      const savedSTO = await stoRepository.save(newSto);

      res.status(201).json(savedSTO);
    } catch (error) {
      console.error("Error creating STO:", error);
      res.status(500).json({ message: "Failed to create STO", error });
    }
  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);
    try {
      const sto = await stoRepository.findOne({ where: { id: Number(id) } });
      if (sto) {
        stoRepository.merge(sto, req.body);
        const updatedSto = await stoRepository.save(sto);
        res.status(200).json(updatedSto);
      } else {
        res.status(404).json({ message: "Purchase Order not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating Purchase Order", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);
    try {
      const result = await purchaseOrderRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Purchase Order not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}