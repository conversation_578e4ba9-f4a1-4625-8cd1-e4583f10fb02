import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { ProductConversion } from "../Models/ProductConversion";
import { Product } from "../Models/Product";
import { Stock } from "../Models/Stock";
import { QueryRunner } from "typeorm";

export class ProductConversionController {
  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const conversionRepository = AppDataSource.getRepository(ProductConversion);
      const conversions = await conversionRepository.find({
        relations: ["fromProduct", "toProduct"]
      });
      res.status(200).json(conversions);
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const conversionRepository = AppDataSource.getRepository(ProductConversion);
      const conversion = await conversionRepository.findOne({
        where: { id: Number(id) },
        relations: ["fromProduct", "toProduct"]
      });
      
      if (conversion) {
        res.status(200).json(conversion);
      } else {
        res.status(404).json({ message: "Conversion not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async create(req: Request, res: Response): Promise<void> {
    const { fromProductId, toProductId, conversionRate } = req.body;
    
    try {
      const conversionRepository = AppDataSource.getRepository(ProductConversion);
      const productRepository = AppDataSource.getRepository(Product);
      
      // Verify both products exist
      const [fromProduct, toProduct] = await Promise.all([
        productRepository.findOne({ where: { id: Number(fromProductId) } }),
        productRepository.findOne({ where: { id: Number(toProductId) } })
      ]);
      
      if (!fromProduct || !toProduct) {
        res.status(400).json({ 
          message: "Invalid products",
          errors: {
            fromProduct: fromProduct ? null : "From product not found",
            toProduct: toProduct ? null : "To product not found"
          }
        });
        return;
      }
      
      // Check if conversion already exists
      const existingConversion = await conversionRepository.findOne({
        where: { 
          fromProductId: Number(fromProductId),
          toProductId: Number(toProductId)
        }
      });
      
      if (existingConversion) {
        // Update the existing conversion rate instead of returning an error
        existingConversion.conversionRate = Number(conversionRate);
        const updatedConversion = await conversionRepository.save(existingConversion);
        res.status(200).json({
          message: "Conversion rate updated",
          conversion: updatedConversion
        });
        return;
      }
      
      const newConversion = conversionRepository.create({
        fromProductId: Number(fromProductId),
        toProductId: Number(toProductId),
        conversionRate: Number(conversionRate)
      });
      
      const savedConversion = await conversionRepository.save(newConversion);
      res.status(201).json(savedConversion);
    } catch (error) {
      res.status(400).json({ message: "Error creating conversion", error });
    }
  }

  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const conversionRepository = AppDataSource.getRepository(ProductConversion);
    
    try {
      const conversion = await conversionRepository.findOne({ where: { id: Number(id) } });
      if (conversion) {
        conversionRepository.merge(conversion, req.body);
        const updatedConversion = await conversionRepository.save(conversion);
        res.status(200).json(updatedConversion);
      } else {
        res.status(404).json({ message: "Conversion not found" });
      }
    } catch (error) {
      res.status(400).json({ message: "Error updating conversion" });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const conversionRepository = AppDataSource.getRepository(ProductConversion);
    
    try {
      const result = await conversionRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "Conversion not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error" });
    }
  }

  public async breakProduct(req: Request, res: Response): Promise<void> {
    const { id } = req.params; // fromProductId
    const { toId, quantity, branchId } = req.body;
    
    if (!toId || !quantity || !branchId) {
      res.status(400).json({ message: "Missing required fields: toId, quantity, or branchId" });
      return;
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      const conversionRepository = queryRunner.manager.getRepository(ProductConversion);
      const stockRepository = queryRunner.manager.getRepository(Stock);
      const productRepository = queryRunner.manager.getRepository(Product);
      
      // Log the input parameters for debugging
      console.log("Break Product Parameters:", { fromId: id, toId, quantity, branchId });
      
      // Verify products exist
      const fromProduct = await productRepository.findOne({ where: { id: Number(id) } });
      const toProduct = await productRepository.findOne({ where: { id: Number(toId) } });
      
      if (!fromProduct || !toProduct) {
        res.status(404).json({ 
          message: "Product not found", 
          details: {
            fromProduct: fromProduct ? "Found" : "Not found",
            toProduct: toProduct ? "Found" : "Not found"
          }
        });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Find conversion rate
      const conversion = await conversionRepository.findOne({
        where: { 
          fromProductId: Number(id),
          toProductId: Number(toId)
        }
      });
      
      if (!conversion) {
        res.status(404).json({ 
          message: "Conversion not found",
          details: {
            fromProductId: Number(id),
            toProductId: Number(toId)
          }
        });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      console.log("Conversion found:", conversion);
      
      // Find source product stock
      const fromStock = await stockRepository.findOne({
        where: { 
          product: { id: Number(id) },
          branch: { id: Number(branchId) }
        }
      });
      
      if (!fromStock) {
        res.status(404).json({ 
          message: "Source product stock not found",
          details: {
            productId: Number(id),
            branchId: Number(branchId)
          }
        });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Find target product stock
      const toStock = await stockRepository.findOne({
        where: { 
          product: { id: Number(toId) },
          branch: { id: Number(branchId) }
        }
      });
      
      if (!toStock) {
        res.status(404).json({ 
          message: "Target product stock not found",
          details: {
            productId: Number(toId),
            branchId: Number(branchId)
          }
        });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      console.log("Stocks found:", { fromStock, toStock });
      
      // Check if enough stock
      if (fromStock.remaining < quantity) {
        res.status(400).json({ 
          message: "Insufficient stock",
          available: fromStock.remaining,
          requested: quantity
        });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Calculate conversion
      const convertedQuantity = quantity * conversion.conversionRate;
      
      // Update stocks
      fromStock.remaining -= quantity;
      toStock.remaining += convertedQuantity;
      
      // Update status if needed
      if (fromStock.remaining === 0) {
        fromStock.status = "ค้าหมด";
      } else if (fromStock.remaining < fromProduct.stock_min) {
        fromStock.status = "ค้าใกล้หมด";
      } else {
        fromStock.status = "ค้าคงอยู่";
      }
      
      
      console.log("Updated stocks (before save):", { 
        fromStock: { id: fromStock.id, remaining: fromStock.remaining },
        toStock: { id: toStock.id, remaining: toStock.remaining }
      });
      
      // Save changes
      await stockRepository.save([fromStock, toStock]);
      
      await queryRunner.commitTransaction();
      
      res.status(200).json({
        message: "Product conversion successful",
        fromProduct: { id: Number(id), quantity: -quantity, remaining: fromStock.remaining },
        toProduct: { id: Number(toId), quantity: convertedQuantity, remaining: toStock.remaining }
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      console.error("Error during product conversion:", error);
      res.status(500).json({ 
        message: "Error during product conversion", 
        error: error instanceof Error ? { 
          name: error.name,
          message: error.message,
          stack: error.stack
        } : error 
      });
    } finally {
      await queryRunner.release();
    }
  }

  public async combineProduct(req: Request, res: Response): Promise<void> {
    const { id } = req.params; // fromProductId (smaller unit)
    const { toId, quantity, branchId } = req.body;
    
    if (!toId || !quantity || !branchId) {
      res.status(400).json({ message: "Missing required fields: toId, quantity, or branchId" });
      return;
    }

    const queryRunner = AppDataSource.createQueryRunner();
    await queryRunner.connect();
    await queryRunner.startTransaction();
    
    try {
      const conversionRepository = queryRunner.manager.getRepository(ProductConversion);
      const stockRepository = queryRunner.manager.getRepository(Stock);
      
      // Find conversion rate (need to look for the reverse of the break operation)
      const conversion = await conversionRepository.findOne({
        where: { 
          fromProductId: Number(toId),
          toProductId: Number(id)
        }
      });
      
      if (!conversion) {
        res.status(404).json({ message: "Conversion not found" });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Find source product stock (smaller unit)
      const fromStock = await stockRepository.findOne({
        where: { 
          product: { id: Number(id) },
          branch: { id: Number(branchId) }
        }
      });
      
      if (!fromStock) {
        res.status(404).json({ message: "Source product stock not found" });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Calculate how many smaller units are needed
      const requiredQuantity = quantity * conversion.conversionRate;
      
      // Check if enough stock of smaller units
      if (fromStock.remaining < requiredQuantity) {
        res.status(400).json({ 
          message: "Insufficient stock",
          available: fromStock.remaining,
          required: requiredQuantity,
          requested: quantity
        });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Find target product stock (larger unit)
      const toStock = await stockRepository.findOne({
        where: { 
          product: { id: Number(toId) },
          branch: { id: Number(branchId) }
        }
      });
      
      if (!toStock) {
        res.status(404).json({ message: "Target product stock not found" });
        await queryRunner.rollbackTransaction();
        return;
      }
      
      // Update stocks
      fromStock.remaining -= requiredQuantity;
      toStock.remaining += quantity;
      
      // Update status if needed
      if (fromStock.remaining === 0) {
        fromStock.status = " ค้าหมด";
      } else if (fromStock.remaining > 0) {
        fromStock.status = " ค่ะ";
      }
      
      if (toStock.status === " ค้าหมด" && toStock.remaining > 0) {
        toStock.status = " ค่ะ";
      }
      
      // Save changes
      await stockRepository.save([fromStock, toStock]);
      
      await queryRunner.commitTransaction();
      
      res.status(200).json({
        message: "Product combination successful",
        fromProduct: { id: Number(id), quantity: -requiredQuantity, remaining: fromStock.remaining },
        toProduct: { id: Number(toId), quantity: quantity, remaining: toStock.remaining }
      });
    } catch (error) {
      await queryRunner.rollbackTransaction();
      res.status(500).json({ message: "Error during product combination", error });
    } finally {
      await queryRunner.release();
    }
  }
}
