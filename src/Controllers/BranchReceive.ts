import { Request, Response } from "express";
import { AppDataSource } from "../Config/db";
import { PurchaseOrder } from "../Models/PurchaseOrder";
import { Stock } from "../Models/Stock";
import { StockTransferSlip } from "../Models/StockTransferSlip";
import { BranchReceive } from "../Models/BranchReceive";
import { BranchReceiveDetails } from "../Models/BranchReceiveDetails";
import { Product } from "../Models/Product";
import { StockTransferOrder } from "../Models/StockTransferOrder";

export class BranchReceiveController {

  public async getAll(req: Request, res: Response): Promise<void> {
    try {
      const brRepository = AppDataSource.getRepository(BranchReceive);
      const br = await brRepository.find({
        relations: ["sts", "user", "source_branch", "destination_branch", "br_details"]
      });
      res.status(200).json(br);
    } catch (error) {
      console.error("Error in getAll:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getById(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    try {
      const brRepository = AppDataSource.getRepository(BranchReceive);
      const br = await brRepository.findOne({
        where: { id: Number(id) },
        relations: ["sts", "user", "source_branch", "destination_branch", "br_details", "br_details.product"]
      });
      if (br) {
        res.status(200).json(br);
      } else {
        res.status(404).json({ message: "BR not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async getAllByFilter(req: Request, res: Response): Promise<void> {
    const { search, filter, startDate, endDate } = req.body;

    try {
      const purchaseOrderRepository = AppDataSource.getRepository(PurchaseOrder);

      const query = purchaseOrderRepository
        .createQueryBuilder("purchase_order")
        .leftJoinAndSelect("purchase_order.supplier", "supplier")
        .leftJoinAndSelect("purchase_order.user", "user")
        .where("1=1");

      // กรองตามคำค้นหาที่ส่งมา
      if (search) {
        query.andWhere(
          `(
            purchase_order.code LIKE :search
            OR user.name LIKE :search
            OR purchase_order.status LIKE :search
          )`,
          { search: `%${search}%` }
        );
      }

      if (filter && search) {
        query.andWhere(`purchase_order.${filter} LIKE :search`, {
          search: `%${search}%`,
        });
      }

      if (startDate) {
        query.andWhere("DATE(purchase_order.date) >= :startDate", {
          startDate: startDate,
        });
      }

      if (endDate) {
        query.andWhere("DATE(purchase_order.date) <= :endDate", {
          endDate: endDate,
        });
      }

      const purchaseOrders = await query.getMany();
      res.status(200).json(purchaseOrders);
    } catch (error) {
      console.error("Error filtering purchase orders:", error);
      res.status(500).json({ message: "Server error", error });
    }
  }

  public async createBRBySTS(req: Request, res: Response): Promise<void> {
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const brRepository = AppDataSource.getRepository(BranchReceive)
    const { stsId } = req.params
    const sts = await stsRepository.findOneOrFail({
      where: { id: Number(stsId) },
      relations: ["user", "source_branch", "destination_branch", "sts_details", "sts_details.product"]
    });
    try {
      // แปลงวันที่เป็น YYYYMMDD
      const formattedDate = new Date().toISOString().slice(0, 10).replace(/-/g, "");

      // ดึง ID ล่าสุด เพื่อใช้ในการ gen code
      const lastBr = await stsRepository.find({
        order: { id: "DESC" },
        take: 1
      });

      const lastId = lastBr.length > 0 ? lastBr[0].id + 1 : 1;

      // สร้างรหัส
      const generatedCode = `BR-${formattedDate}-${lastId}`;

      // แปลง sts_details → br_details
      // const brDetails = sts.sts_details.map(detail => ({
      //   id: detail.id,
      //   sto: sts,
      //   product: detail.product,
      //   sto_quantity: detail.sts_quantity,
      //   lot_number: detail.lot_number
      // }));

      // สร้าง BR ใหม่
      const newBr = brRepository.create({
        sts: sts,
        code: generatedCode,
        sts_code: sts.code,
        source_branch: sts.source_branch,
        destination_branch: sts.destination_branch,
        receive_date: new Date(),
        user: sts.user,
        note: sts.note,
        status: 'ระหว่างดำเนินการ', // กำหนดสถานะเริ่มต้น
      });
      // บันทึกลง DB เพื่อให้ได้ newSts.id
      const savedBR = await brRepository.save(newBr);

      // แปลง detail และผูกกลับหา parent
      const brDetailRepository = AppDataSource.getRepository(BranchReceiveDetails);
      const stockRepository = AppDataSource.getRepository(Stock);

      const brDetails = await Promise.all(
        sts.sts_details.map(async (detail) => {
          // const lotStock = await stockRepository.findOneOrFail({
          //   where: {
          //     branch: { id: sto.destination_branch.id },
          //     product: { id: detail.product.id }
          //   },
          //   relations: ["product", "branch"]
          // });

          return brDetailRepository.create({
            product: detail.product,
            receive_quantity: detail.sts_quantity,
            lot_number: detail.lot_number,
            sts_details: detail,
            br: newBr
          });
        })
      );

      // save ทั้ง array
      await brDetailRepository.save(brDetails);
      savedBR.br_details = brDetails;

      const response = {
        ...savedBR,
        br_details: brDetails.map(detail => ({
          id: detail.id,
          product: detail.product,
          receive_quantity: detail.receive_quantity,
          lot_number: detail.lot_number,
          // sts: { id: detail.sts.id }
        }))
      };

      res.status(201).json(response);

      // res.status(201).json(savedSTS);
    } catch (error) {
      console.error("Error creating BR:", error);
      res.status(500).json({ message: "Failed to create BR", error });
    }
  }


  public async update(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const brRepository = AppDataSource.getRepository(BranchReceive);
    const stsRepository = AppDataSource.getRepository(StockTransferSlip);
    const stoRepository = AppDataSource.getRepository(StockTransferOrder);

    const stockRepository = AppDataSource.getRepository(Stock)
    const productRepository = AppDataSource.getRepository(Product)

    try {
      const br = await brRepository.findOneOrFail({ where: { id: Number(id) }, relations: ["sts", "user", "source_branch", "destination_branch", "br_details", "br_details.product"] });
      const sts = await stsRepository.findOneOrFail({ where: { id: br.sts.id }, relations: ["sto"] });
      const sto = await stoRepository.findOneOrFail({ where: { id: sts.sto.id } });
      br.status = 'เสร็จสมบูรณ์'
      await stsRepository.update(sts.id, {
        transfer_status: 'ปลายทางรับทราบ'
      });
      await stoRepository.update(sto.id, {
        transfer_status: 'ปลายทางรับทราบ'
      });

      br.complete_date = new Date()
      const updateStock = await Promise.all(
        br.br_details.map(async (detail) => {
          console.log(br.destination_branch.id)
          console.log(detail.product.id)
          console.log(detail.lot_number)
          const stock = await stockRepository.findOneOrFail({
            where: {
              branch: { id: br.destination_branch.id },
              product: { id: detail.product.id },
              lot_number: detail.lot_number
            },
            relations: ["product", "branch"]
          });
          //update status to stock
          const product = await productRepository.findOneOrFail({ where: { id: stock.product.id } });
          if (product) {
            if (stock.remaining === 0) {
              stock.status = "สินค้าหมด";
            } else if (stock.remaining < product.stock_min) {
              stock.status = "สินค้าใกล้หมด";
            } else {
              stock.status = "สินค้าคงอยู่";
            }
          }
          //update remaining to stock
          stock.remaining += detail.receive_quantity
          await stockRepository.save(stock)
          console.log(stock)
        })
      );
      const updatedBR = await brRepository.save(br);
      res.status(200).json(updatedBR);

    } catch (error) {
      res.status(500).json({ message: "Error updating BR", error });
    }
  }

  public async delete(req: Request, res: Response): Promise<void> {
    const { id } = req.params;
    const brRepository = AppDataSource.getRepository(BranchReceive);
    try {
      const result = await brRepository.delete(id);
      if (result.affected) {
        res.status(204).send();
      } else {
        res.status(404).json({ message: "BR not found" });
      }
    } catch (error) {
      res.status(500).json({ message: "Server error", error });
    }
  }
}